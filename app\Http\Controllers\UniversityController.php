<?php

namespace App\Http\Controllers;

use App\Models\Campus;
use App\Models\School;
use App\Models\User;
use App\Models\Organization;
use App\Models\Group;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UniversityController extends Controller
{
    /**
     * Display the main university overview page
     */
    public function index()
    {
        $user = Auth::user();
        $school = $user->school;
        
        if (!$school) {
            // If user doesn't have a school, redirect to profile to complete setup
            return redirect()->route('profile.show')->with('error', 'Please complete your profile by selecting your school and campus.');
        }

        // Get university statistics
        $stats = [
            'total_students' => $school->users()->count(),
            'total_organizations' => $school->organizations()->where('status', 'active')->count(),
            'total_groups' => $school->groups()->where('status', 'active')->count(),
            'total_campuses' => $school->activeCampuses()->count(),
        ];

        return view('university.index', compact('school', 'stats'));
    }

    /**
     * Display all campuses for the user's school
     */
    public function campuses()
    {
        $user = Auth::user();
        $school = $user->school;
        
        if (!$school) {
            return redirect()->route('profile.show')->with('error', 'Please complete your profile by selecting your school and campus.');
        }

        $campuses = $school->activeCampuses()
            ->withCount(['users', 'organizations', 'groups'])
            ->orderBy('is_main_campus', 'desc')
            ->orderBy('name')
            ->get();

        // Add image paths for each campus
        $campuses->each(function ($campus) {
            $campus->image_path = $this->getCampusImagePath($campus->slug);
        });

        return view('university.campuses', compact('school', 'campuses'));
    }

    /**
     * Display a specific campus page with detailed information
     */
    public function campus(Campus $campus)
    {
        $user = Auth::user();
        
        // Ensure the campus belongs to the user's school
        if ($user->school_id !== $campus->school_id) {
            abort(404);
        }

        // Get campus statistics using helper methods
        $stats = $campus->getStatistics();

        // Get recent data with proper relationships loaded
        $organizations = $campus->organizations()
            ->where('status', 'active')
            ->with(['creator', 'activeMembers'])
            ->withCount('activeMembers')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        $groups = $campus->groups()
            ->where('status', 'active')
            ->with(['creator', 'organization', 'school', 'campus', 'activeMembers'])
            ->withCount('activeMembers')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        $students = $campus->users()
            ->where('role', 'student')
            ->orderBy('created_at', 'desc')
            ->limit(12)
            ->get();

        $posts = Post::whereIn('user_id', $campus->users()->pluck('id'))
            ->where('status', 'published')
            ->with(['user', 'tags', 'reactions', 'group', 'organization'])
            ->withCount(['comments', 'reactions'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $campus->image_path = $this->getCampusImagePath($campus->slug);

        // Debug information
        \Log::info('Campus data loaded', [
            'campus' => $campus->name,
            'organizations_count' => $organizations->count(),
            'groups_count' => $groups->count(),
            'students_count' => $students->count(),
            'posts_count' => $posts->count(),
        ]);

        return view('university.campus', compact('campus', 'stats', 'organizations', 'groups', 'students', 'posts'));
    }

    /**
     * Display offices and directory for the university
     */
    public function offices()
    {
        $user = Auth::user();
        $school = $user->school;
        
        if (!$school) {
            return redirect()->route('profile.show')->with('error', 'Please complete your profile by selecting your school and campus.');
        }

        // For now, we'll show a placeholder page
        // In the future, this could be populated with actual office data
        $offices = [
            [
                'name' => 'Office of the President',
                'description' => 'The highest administrative office of the university.',
                'contact' => '<EMAIL>',
                'phone' => '+63 64 477 8442',
            ],
            [
                'name' => 'Office of the Vice President for Academic Affairs',
                'description' => 'Oversees all academic programs and activities.',
                'contact' => '<EMAIL>',
                'phone' => '+63 64 477 8442',
            ],
            [
                'name' => 'Office of Student Affairs',
                'description' => 'Handles student services and activities.',
                'contact' => '<EMAIL>',
                'phone' => '+63 64 477 8442',
            ],
            [
                'name' => 'Registrar\'s Office',
                'description' => 'Manages student records and enrollment.',
                'contact' => '<EMAIL>',
                'phone' => '+63 64 477 8442',
            ],
            [
                'name' => 'Finance Office',
                'description' => 'Handles financial transactions and student accounts.',
                'contact' => '<EMAIL>',
                'phone' => '+63 64 477 8442',
            ],
            [
                'name' => 'Library Services',
                'description' => 'Provides library and information services.',
                'contact' => '<EMAIL>',
                'phone' => '+63 64 477 8442',
            ],
        ];

        return view('university.offices', compact('school', 'offices'));
    }

    /**
     * Get the image path for a campus based on its slug
     */
    private function getCampusImagePath($slug)
    {
        $imageMap = [
            'access-campus' => 'access-campus-1024x576.jpg',
            'isulan-campus' => 'isulan-campus-1024x576.jpg',
            'bagumbayan-campus' => 'bagumbayan-campus_-1024x576.jpg',
            'lutayan-campus' => 'lutayan-campus_-1024x576.jpg',
            'kalamansig-campus' => 'kalamansig-campus_-1024x576.jpg',
            'palimbang-campus' => 'palimbang-campus_-1024x576.jpg',
            'tacurong-campus' => 'tacurong-campus_-1024x576.jpg',
        ];

        return isset($imageMap[$slug]) 
            ? asset('assets/sksu-campuses/' . $imageMap[$slug])
            : asset('assets/sksu-campuses/access-campus-1024x576.jpg'); // Default fallback
    }
}
