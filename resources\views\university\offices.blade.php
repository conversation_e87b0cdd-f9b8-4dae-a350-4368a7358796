@extends('layouts.unilink-layout')

@section('title', 'Offices & Directory - ' . ($school->abbreviation ?? 'University'))

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-2 text-sm text-custom-dark mb-2">
            <a href="{{ route('university.index') }}" class="hover:text-custom-green">University</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Offices & Directory</span>
        </div>
        <h1 class="text-3xl font-bold text-custom-darkest">{{ $school->abbreviation }} Offices & Directory</h1>
        <p class="text-custom-dark mt-2">Find contact information for university offices and departments</p>
    </div>

    <!-- Offices Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($offices as $office)
            <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6 hover:shadow-md transition-shadow duration-200">
                <!-- Office Icon -->
                <div class="flex items-center space-x-4 mb-4">
                    <div class="p-3 rounded-full bg-custom-green">
                        <x-svg-icon name="Organizations" class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-custom-darkest">{{ $office['name'] }}</h3>
                    </div>
                </div>

                <!-- Office Description -->
                @if(isset($office['description']))
                    <p class="text-custom-dark text-sm mb-4">{{ $office['description'] }}</p>
                @endif

                <!-- Contact Information -->
                <div class="space-y-3">
                    @if(isset($office['contact']))
                        <div class="flex items-center space-x-3">
                            <svg class="w-4 h-4 text-custom-dark flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <div>
                                <p class="text-xs font-medium text-custom-darkest">Email</p>
                                <a href="mailto:{{ $office['contact'] }}" class="text-custom-green hover:underline text-sm">{{ $office['contact'] }}</a>
                            </div>
                        </div>
                    @endif

                    @if(isset($office['phone']))
                        <div class="flex items-center space-x-3">
                            <svg class="w-4 h-4 text-custom-dark flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <div>
                                <p class="text-xs font-medium text-custom-darkest">Phone</p>
                                <a href="tel:{{ $office['phone'] }}" class="text-custom-green hover:underline text-sm">{{ $office['phone'] }}</a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        @endforeach
    </div>

    <!-- Additional Information -->
    <div class="mt-12 bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
        <h2 class="text-xl font-bold text-custom-darkest mb-4">General Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-custom-darkest mb-3">Office Hours</h3>
                <div class="space-y-2 text-sm text-custom-dark">
                    <p><span class="font-medium">Monday - Friday:</span> 8:00 AM - 5:00 PM</p>
                    <p><span class="font-medium">Saturday:</span> 8:00 AM - 12:00 PM</p>
                    <p><span class="font-medium">Sunday:</span> Closed</p>
                </div>
            </div>
            
            <div>
                <h3 class="text-lg font-semibold text-custom-darkest mb-3">Emergency Contacts</h3>
                <div class="space-y-2 text-sm text-custom-dark">
                    <p><span class="font-medium">Security Office:</span> <a href="tel:+63644778442" class="text-custom-green hover:underline">+63 64 477 8442</a></p>
                    <p><span class="font-medium">Medical Clinic:</span> <a href="tel:+63644778442" class="text-custom-green hover:underline">+63 64 477 8442</a></p>
                    <p><span class="font-medium">Main Office:</span> <a href="tel:+63644778442" class="text-custom-green hover:underline">+63 64 477 8442</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
        <h2 class="text-xl font-bold text-custom-darkest mb-4">Quick Links</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            @if($school->website)
                <a href="{{ $school->website }}" target="_blank" class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg hover:bg-custom-second-darkest transition-colors duration-200">
                    <svg class="w-5 h-5 text-custom-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9 9s4-9 9-9" />
                    </svg>
                    <span class="text-custom-darkest font-medium">Official Website</span>
                </a>
            @endif

            <a href="{{ route('organizations.index') }}" class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg hover:bg-custom-second-darkest transition-colors duration-200">
                <x-svg-icon name="Organizations" class="w-5 h-5 text-custom-green" />
                <span class="text-custom-darkest font-medium">Organizations</span>
            </a>

            <a href="{{ route('groups.index') }}" class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg hover:bg-custom-second-darkest transition-colors duration-200">
                <x-svg-icon name="Groups" class="w-5 h-5 text-custom-green" />
                <span class="text-custom-darkest font-medium">Student Groups</span>
            </a>

            <a href="{{ route('scholarships.index') }}" class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg hover:bg-custom-second-darkest transition-colors duration-200">
                <x-svg-icon name="Scholarships" class="w-5 h-5 text-custom-green" />
                <span class="text-custom-darkest font-medium">Scholarships</span>
            </a>
        </div>
    </div>

    <!-- Contact Form Section -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
        <h2 class="text-xl font-bold text-custom-darkest mb-4">Need Help?</h2>
        <p class="text-custom-dark mb-4">Can't find what you're looking for? Contact us directly and we'll help you get in touch with the right office.</p>
        
        <div class="flex flex-col sm:flex-row gap-4">
            @if($school->contact_info && isset($school->contact_info['email']))
                <a href="mailto:{{ $school->contact_info['email'] }}" 
                   class="inline-flex items-center justify-center px-4 py-2 border border-custom-green text-custom-green rounded-lg hover:bg-custom-green hover:text-white transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Send Email
                </a>
            @endif

            @if($school->contact_info && isset($school->contact_info['phone']))
                <a href="tel:{{ $school->contact_info['phone'] }}" 
                   class="inline-flex items-center justify-center px-4 py-2 border border-custom-green text-custom-green rounded-lg hover:bg-custom-green hover:text-white transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    Call Now
                </a>
            @endif
        </div>
    </div>
</div>
@endsection
