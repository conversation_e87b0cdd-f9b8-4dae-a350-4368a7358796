<x-layouts.unilink-layout>
    <x-slot name="title">University - {{ $school->name ?? 'UniLink' }}</x-slot>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- University Header -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest mb-6">
        <div class="p-6">
            <div class="flex items-center space-x-4">
                @if($school->logo)
                    <img src="{{ asset('storage/' . $school->logo) }}" alt="{{ $school->name }}" class="w-16 h-16 rounded-lg object-cover">
                @else
                    <div class="w-16 h-16 bg-custom-green rounded-lg flex items-center justify-center">
                        <x-svg-icon name="University" class="w-8 h-8 text-white" />
                    </div>
                @endif
                <div>
                    <h1 class="text-3xl font-bold text-custom-darkest">{{ $school->name }}</h1>
                    <p class="text-custom-dark mt-1">{{ $school->abbreviation }}</p>
                    @if($school->description)
                        <p class="text-custom-dark mt-2">{{ $school->description }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <x-svg-icon name="Profile" class="w-6 h-6 text-blue-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Total Students</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_students']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <x-svg-icon name="Organizations" class="w-6 h-6 text-green-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Organizations</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_organizations']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <x-svg-icon name="Groups" class="w-6 h-6 text-purple-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Student Groups</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_groups']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <x-svg-icon name="University" class="w-6 h-6 text-orange-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Campuses</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_campuses']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <a href="{{ route('university.campuses') }}" class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center space-x-4">
                <div class="p-3 rounded-full bg-custom-green">
                    <x-svg-icon name="Groups" class="w-6 h-6 text-white" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-custom-darkest">Explore Campuses</h3>
                    <p class="text-custom-dark">View all {{ $school->abbreviation }} campuses and their information</p>
                </div>
            </div>
        </a>

        <a href="{{ route('university.offices') }}" class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center space-x-4">
                <div class="p-3 rounded-full bg-custom-green">
                    <x-svg-icon name="Organizations" class="w-6 h-6 text-white" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-custom-darkest">Offices & Directory</h3>
                    <p class="text-custom-dark">Find contact information for university offices</p>
                </div>
            </div>
        </a>

        <a href="{{ route('organizations.index') }}" class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center space-x-4">
                <div class="p-3 rounded-full bg-custom-green">
                    <x-svg-icon name="Organizations" class="w-6 h-6 text-white" />
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-custom-darkest">Organizations</h3>
                    <p class="text-custom-dark">Browse all student organizations</p>
                </div>
            </div>
        </a>
    </div>

    <!-- University Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- About Section -->
        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <h2 class="text-xl font-bold text-custom-darkest mb-4">About {{ $school->abbreviation }}</h2>
            @if($school->description)
                <p class="text-custom-dark mb-4">{{ $school->description }}</p>
            @endif
            
            @if($school->address)
                <div class="flex items-start space-x-3 mb-3">
                    <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">Address</p>
                        <p class="text-custom-dark">{{ $school->address }}</p>
                    </div>
                </div>
            @endif

            @if($school->website)
                <div class="flex items-start space-x-3 mb-3">
                    <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">Website</p>
                        <a href="{{ $school->website }}" target="_blank" class="text-custom-green hover:underline">{{ $school->website }}</a>
                    </div>
                </div>
            @endif

            @if($school->contact_info && isset($school->contact_info['phone']))
                <div class="flex items-start space-x-3 mb-3">
                    <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">Phone</p>
                        <p class="text-custom-dark">{{ $school->contact_info['phone'] }}</p>
                    </div>
                </div>
            @endif

            @if($school->contact_info && isset($school->contact_info['email']))
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">Email</p>
                        <a href="mailto:{{ $school->contact_info['email'] }}" class="text-custom-green hover:underline">{{ $school->contact_info['email'] }}</a>
                    </div>
                </div>
            @endif
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <h2 class="text-xl font-bold text-custom-darkest mb-4">University Highlights</h2>
            <div class="space-y-4">
                <div class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg">
                    <div class="p-2 rounded-full bg-custom-green">
                        <x-svg-icon name="University" class="w-4 h-4 text-white" />
                    </div>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">{{ $stats['total_campuses'] }} Active Campuses</p>
                        <p class="text-xs text-custom-dark">Serving students across Sultan Kudarat</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg">
                    <div class="p-2 rounded-full bg-blue-500">
                        <x-svg-icon name="Profile" class="w-4 h-4 text-white" />
                    </div>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">{{ number_format($stats['total_students']) }} Students Enrolled</p>
                        <p class="text-xs text-custom-dark">Active student community</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg">
                    <div class="p-2 rounded-full bg-green-500">
                        <x-svg-icon name="Organizations" class="w-4 h-4 text-white" />
                    </div>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">{{ number_format($stats['total_organizations']) }} Organizations</p>
                        <p class="text-xs text-custom-dark">Student organizations and clubs</p>
                    </div>
                </div>

                <div class="flex items-center space-x-3 p-3 bg-custom-lightest rounded-lg">
                    <div class="p-2 rounded-full bg-purple-500">
                        <x-svg-icon name="Groups" class="w-4 h-4 text-white" />
                    </div>
                    <div>
                        <p class="text-sm font-medium text-custom-darkest">{{ number_format($stats['total_groups']) }} Student Groups</p>
                        <p class="text-xs text-custom-dark">Active student communities</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-layouts.unilink-layout>
