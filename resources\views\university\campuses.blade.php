@extends('layouts.unilink-layout')

@section('title', 'Campuses - ' . ($school->abbreviation ?? 'University'))

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-2 text-sm text-custom-dark mb-2">
            <a href="{{ route('university.index') }}" class="hover:text-custom-green">University</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span>Campuses</span>
        </div>
        <h1 class="text-3xl font-bold text-custom-darkest">{{ $school->abbreviation }} Campuses</h1>
        <p class="text-custom-dark mt-2">Explore all {{ $school->name }} campuses across Sultan Kudarat</p>
    </div>

    <!-- Campus Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($campuses as $campus)
            <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest overflow-hidden hover:shadow-md transition-shadow duration-200">
                <!-- Campus Image -->
                <div class="relative h-48 bg-gray-200">
                    <img src="{{ $campus->image_path }}" 
                         alt="{{ $campus->name }}" 
                         class="w-full h-full object-cover">
                    @if($campus->is_main_campus)
                        <div class="absolute top-3 left-3">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-custom-green text-white">
                                Main Campus
                            </span>
                        </div>
                    @endif
                </div>

                <!-- Campus Info -->
                <div class="p-6">
                    <h3 class="text-xl font-bold text-custom-darkest mb-2">{{ $campus->name }}</h3>
                    
                    @if($campus->description)
                        <p class="text-custom-dark text-sm mb-4 line-clamp-2">{{ $campus->description }}</p>
                    @endif

                    @if($campus->address)
                        <div class="flex items-start space-x-2 mb-4">
                            <svg class="w-4 h-4 text-custom-dark mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <p class="text-custom-dark text-sm">{{ $campus->address }}</p>
                        </div>
                    @endif

                    <!-- Campus Statistics -->
                    <div class="grid grid-cols-3 gap-4 mb-4">
                        <div class="text-center">
                            <p class="text-lg font-bold text-custom-darkest">{{ number_format($campus->users_count) }}</p>
                            <p class="text-xs text-custom-dark">Students</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-custom-darkest">{{ number_format($campus->organizations_count) }}</p>
                            <p class="text-xs text-custom-dark">Organizations</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-custom-darkest">{{ number_format($campus->groups_count) }}</p>
                            <p class="text-xs text-custom-dark">Groups</p>
                        </div>
                    </div>

                    <!-- View Campus Button -->
                    <a href="{{ route('university.campus', $campus) }}" 
                       class="w-full bg-custom-green text-white text-center py-2 px-4 rounded-lg hover:bg-custom-green-dark transition-colors duration-200 inline-block">
                        View Campus
                    </a>
                </div>
            </div>
        @endforeach
    </div>

    @if($campuses->isEmpty())
        <div class="text-center py-12">
            <div class="max-w-md mx-auto">
                <div class="p-6 bg-white rounded-lg shadow-sm border border-custom-second-darkest">
                    <x-svg-icon name="University" class="w-12 h-12 text-custom-dark mx-auto mb-4" />
                    <h3 class="text-lg font-medium text-custom-darkest mb-2">No Campuses Found</h3>
                    <p class="text-custom-dark">No active campuses are currently available for {{ $school->name }}.</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Campus Overview Stats -->
    @if($campuses->isNotEmpty())
        <div class="mt-12 bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <h2 class="text-xl font-bold text-custom-darkest mb-6">Campus Overview</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="p-4 bg-blue-100 rounded-full w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <x-svg-icon name="University" class="w-8 h-8 text-blue-600" />
                    </div>
                    <p class="text-2xl font-bold text-custom-darkest">{{ $campuses->count() }}</p>
                    <p class="text-custom-dark">Total Campuses</p>
                </div>
                <div class="text-center">
                    <div class="p-4 bg-green-100 rounded-full w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <x-svg-icon name="Profile" class="w-8 h-8 text-green-600" />
                    </div>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($campuses->sum('users_count')) }}</p>
                    <p class="text-custom-dark">Total Students</p>
                </div>
                <div class="text-center">
                    <div class="p-4 bg-purple-100 rounded-full w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <x-svg-icon name="Organizations" class="w-8 h-8 text-purple-600" />
                    </div>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($campuses->sum('organizations_count')) }}</p>
                    <p class="text-custom-dark">Total Organizations</p>
                </div>
                <div class="text-center">
                    <div class="p-4 bg-orange-100 rounded-full w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <x-svg-icon name="Groups" class="w-8 h-8 text-orange-600" />
                    </div>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($campuses->sum('groups_count')) }}</p>
                    <p class="text-custom-dark">Total Groups</p>
                </div>
            </div>
        </div>
    @endif
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
