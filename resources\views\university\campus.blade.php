<x-layouts.unilink-layout>
    <x-slot name="title">{{ $campus->name }} - {{ $campus->school->abbreviation ?? 'University' }}</x-slot>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Breadcrumb -->
    <div class="flex items-center space-x-2 text-sm text-custom-dark mb-6">
        <a href="{{ route('university.index') }}" class="hover:text-custom-green">University</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <a href="{{ route('university.campuses') }}" class="hover:text-custom-green">Campuses</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <span>{{ $campus->name }}</span>
    </div>

    <!-- Campus Header -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest mb-6 overflow-hidden">
        <!-- Campus Image -->
        <div class="relative h-64 bg-gray-200">
            <img src="{{ $campus->image_path }}" 
                 alt="{{ $campus->name }}" 
                 class="w-full h-full object-cover">
            @if($campus->is_main_campus)
                <div class="absolute top-4 left-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-custom-green text-white">
                        Main Campus
                    </span>
                </div>
            @endif
        </div>

        <!-- Campus Info -->
        <div class="p-6">
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-custom-darkest mb-2">{{ $campus->name }}</h1>
                    @if($campus->description)
                        <p class="text-custom-dark mb-4">{{ $campus->description }}</p>
                    @endif
                    @if($campus->address)
                        <div class="flex items-start space-x-2">
                            <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <p class="text-custom-dark">{{ $campus->address }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <x-svg-icon name="Profile" class="w-6 h-6 text-blue-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Students</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_students']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <x-svg-icon name="Organizations" class="w-6 h-6 text-green-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Organizations</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_organizations']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <x-svg-icon name="Groups" class="w-6 h-6 text-purple-600" />
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Groups</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_groups']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-indigo-100">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Officers</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['total_officers']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Recent Posts</p>
                    <p class="text-2xl font-bold text-custom-darkest">{{ number_format($stats['recent_posts']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabbed Content -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest" id="campus-tabs">
        <!-- Tab Navigation -->
        <div class="border-b border-custom-second-darkest">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="showTab('overview')" id="tab-overview"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-custom-green text-custom-green">
                    Overview
                </button>
                <button onclick="showTab('organizations')" id="tab-organizations"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Organizations ({{ $organizations->count() }})
                </button>
                <button onclick="showTab('groups')" id="tab-groups"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Groups ({{ $groups->count() }})
                </button>
                <button onclick="showTab('students')" id="tab-students"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Students ({{ $students->count() }})
                </button>
                <button onclick="showTab('officers')" id="tab-officers"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Officers ({{ $officers->count() }})
                </button>
                <button onclick="showTab('posts')" id="tab-posts"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Posts ({{ $posts->count() }})
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">

            <!-- Overview Tab -->
            <div id="content-overview" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Campus Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-custom-darkest mb-4">Campus Information</h3>
                        <div class="space-y-4">
                            @if($campus->description)
                                <div>
                                    <p class="text-sm font-medium text-custom-darkest">Description</p>
                                    <p class="text-custom-dark mt-1">{{ $campus->description }}</p>
                                </div>
                            @endif
                            
                            @if($campus->address)
                                <div>
                                    <p class="text-sm font-medium text-custom-darkest">Address</p>
                                    <p class="text-custom-dark mt-1">{{ $campus->address }}</p>
                                </div>
                            @endif

                            @if($campus->contact_info)
                                @if(isset($campus->contact_info['phone']))
                                    <div>
                                        <p class="text-sm font-medium text-custom-darkest">Phone</p>
                                        <p class="text-custom-dark mt-1">{{ $campus->contact_info['phone'] }}</p>
                                    </div>
                                @endif
                                
                                @if(isset($campus->contact_info['email']))
                                    <div>
                                        <p class="text-sm font-medium text-custom-darkest">Email</p>
                                        <a href="mailto:{{ $campus->contact_info['email'] }}" class="text-custom-green hover:underline mt-1 block">{{ $campus->contact_info['email'] }}</a>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div>
                        <h3 class="text-lg font-semibold text-custom-darkest mb-4">Quick Statistics</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Total Students</span>
                                <span class="font-semibold text-custom-darkest">{{ number_format($stats['total_students']) }}</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Active Organizations</span>
                                <span class="font-semibold text-custom-darkest">{{ number_format($stats['total_organizations']) }}</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Student Groups</span>
                                <span class="font-semibold text-custom-darkest">{{ number_format($stats['total_groups']) }}</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Recent Posts</span>
                                <span class="font-semibold text-custom-darkest">{{ number_format($stats['recent_posts']) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Organizations Tab -->
            <div id="content-organizations" class="tab-content" style="display: none;">
                @if($organizations->isNotEmpty())
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($organizations as $organization)
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                                <!-- Cover Image -->
                                <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                                    @if($organization->cover_image)
                                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image) }}" alt="{{ $organization->name }}" class="w-full h-full object-cover">
                                    @endif

                                    <!-- Logo -->
                                    <div class="absolute -bottom-6 left-4">
                                        <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                                            @if($organization->logo)
                                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-10 h-10 rounded-lg object-cover">
                                            @else
                                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-blue-600 font-semibold text-sm">{{ substr($organization->name, 0, 2) }}</span>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="p-4 pt-8">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900 truncate">{{ $organization->name }}</h3>
                                        <span class="text-sm text-gray-500">{{ $organization->active_members_count }} members</span>
                                    </div>

                                    @if($organization->description)
                                        <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ Str::limit($organization->description, 100) }}</p>
                                    @endif

                                    @if($organization->creator)
                                        <p class="text-xs text-gray-500 mb-4">Created by {{ $organization->creator->name }}</p>
                                    @endif

                                    <!-- Action Buttons -->
                                    <div class="flex space-x-2">
                                        <a href="{{ route('organizations.show', $organization) }}" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 text-sm font-medium">
                                            View Details
                                        </a>
                                        @auth
                                            @php
                                                $userMembership = $organization->members()->where('user_id', auth()->id())->first();
                                                $isFollowing = auth()->user()->followedOrganizations()->where('organization_id', $organization->id)->exists();
                                            @endphp

                                            @if($userMembership && $userMembership->pivot->status === 'active')
                                                <button disabled class="flex-1 bg-gray-100 text-gray-600 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                                    Member
                                                </button>
                                            @elseif($userMembership && $userMembership->pivot->status === 'pending')
                                                <button disabled class="flex-1 bg-yellow-100 text-yellow-600 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                                    Pending
                                                </button>
                                            @else
                                                <form action="{{ route('organizations.join', $organization) }}" method="POST" class="flex-1">
                                                    @csrf
                                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 text-sm font-medium">
                                                        Join
                                                    </button>
                                                </form>
                                            @endif
                                        @endauth
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <x-svg-icon name="Organizations" class="w-12 h-12 text-custom-dark mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Organizations</h3>
                        <p class="text-custom-dark">No organizations are currently registered for this campus.</p>
                    </div>
                @endif
            </div>

            <!-- Groups Tab -->
            <div id="content-groups" class="tab-content" style="display: none;">
                @if($groups->isNotEmpty())
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($groups as $group)
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                                <!-- Cover Image -->
                                <div class="h-32 bg-gradient-to-r from-green-500 to-blue-600 relative">
                                    @if($group->cover_image)
                                        <img src="{{ Storage::disk('public')->url($group->cover_image) }}"
                                             alt="{{ $group->name }}"
                                             class="w-full h-full object-cover">
                                    @endif

                                    <!-- Privacy Badge -->
                                    <div class="absolute top-3 right-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ ucfirst($group->visibility) }}
                                        </span>
                                    </div>
                                </div>

                                <div class="p-4">
                                    <!-- Group Logo and Info -->
                                    <div class="flex items-start space-x-3 -mt-4 mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center border-2 border-white">
                                                @if($group->logo)
                                                    <img src="{{ Storage::disk('public')->url($group->logo) }}"
                                                         alt="{{ $group->name }}"
                                                         class="w-10 h-10 rounded-lg object-cover">
                                                @else
                                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                        <span class="text-blue-600 font-bold text-sm">{{ substr($group->name, 0, 2) }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>

                                        <div class="flex-1 min-w-0 pt-2">
                                            <h3 class="text-lg font-semibold text-gray-900 truncate">
                                                <a href="{{ route('groups.show', $group) }}" class="hover:text-blue-600">
                                                    {{ $group->name }}
                                                </a>
                                            </h3>
                                            @if($group->organization)
                                                <p class="text-sm text-gray-500 truncate">{{ $group->organization->name }}</p>
                                            @endif
                                            @if($group->school || $group->campus)
                                                <p class="text-xs text-gray-400 truncate">
                                                    @if($group->school)
                                                        {{ $group->school->abbreviation ?? $group->school->name }}
                                                        @if($group->campus)
                                                            - {{ $group->campus->name }}
                                                        @endif
                                                    @elseif($group->campus)
                                                        {{ $group->campus->name }}
                                                    @endif
                                                </p>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Description -->
                                    @if($group->description)
                                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $group->description }}</p>
                                    @endif

                                    <!-- Stats -->
                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                            </svg>
                                            {{ $group->active_members_count }} members
                                        </div>
                                        @if($group->creator)
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                                {{ $group->creator->name }}
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Action Button -->
                                    <div class="flex justify-end">
                                        <a href="{{ route('groups.show', $group) }}"
                                           class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                                            View Group
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <x-svg-icon name="Groups" class="w-12 h-12 text-custom-dark mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Groups</h3>
                        <p class="text-custom-dark">No student groups are currently active for this campus.</p>
                    </div>
                @endif
            </div>

            <!-- Students Tab -->
            <div id="content-students" class="tab-content" style="display: none;">
                @if($students->isNotEmpty())
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach($students as $student)
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow duration-200">
                                <!-- Profile Picture -->
                                <div class="relative mb-4">
                                    @if($student->avatar)
                                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($student->avatar) }}"
                                             alt="{{ $student->name }}"
                                             class="w-20 h-20 rounded-full mx-auto object-cover border-4 border-white shadow-md">
                                    @else
                                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto flex items-center justify-center border-4 border-white shadow-md">
                                            <span class="text-white font-bold text-2xl">{{ substr($student->name, 0, 1) }}</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Student Info -->
                                <div class="space-y-2">
                                    <h4 class="text-lg font-semibold text-gray-900">{{ $student->name }}</h4>

                                    @if($student->course_program)
                                        <p class="text-sm text-gray-600 font-medium">{{ $student->course_program }}</p>
                                    @endif

                                    @if($student->year_level)
                                        <p class="text-xs text-gray-500">{{ $student->year_level }}</p>
                                    @endif

                                    @if($student->college_department)
                                        <p class="text-xs text-gray-500">{{ $student->college_department }}</p>
                                    @endif

                                    <!-- Campus Info -->
                                    <div class="flex items-center justify-center text-xs text-gray-400 mt-2">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        {{ $campus->name }}
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="mt-4 flex space-x-2">
                                    <a href="{{ route('profile.user', $student) }}"
                                       class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                                        View Profile
                                    </a>
                                    @auth
                                        @if(auth()->id() !== $student->id)
                                            @if(auth()->user()->isFollowingUser($student))
                                                <form action="{{ route('users.unfollow', $student) }}" method="POST" class="flex-1">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="w-full bg-gray-200 text-gray-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-gray-300 transition-colors">
                                                        Following
                                                    </button>
                                                </form>
                                            @else
                                                <form action="{{ route('users.follow', $student) }}" method="POST" class="flex-1">
                                                    @csrf
                                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                                        Follow
                                                    </button>
                                                </form>
                                            @endif
                                        @endif
                                    @endauth
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <x-svg-icon name="Profile" class="w-12 h-12 text-custom-dark mx-auto mb-4" />
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Students</h3>
                        <p class="text-custom-dark">No students are currently registered for this campus.</p>
                    </div>
                @endif
            </div>

            <!-- Organization Officers Tab -->
            <div id="content-officers" class="tab-content" style="display: none;">
                @if($officers->isNotEmpty())
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        @foreach($officers as $officer)
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow duration-200">
                                <!-- Profile Picture -->
                                <div class="mb-4">
                                    @if($officer->profile_picture)
                                        <img src="{{ asset('storage/' . $officer->profile_picture) }}"
                                             alt="{{ $officer->name }}"
                                             class="w-16 h-16 rounded-full mx-auto object-cover">
                                    @else
                                        <div class="w-16 h-16 rounded-full mx-auto bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                                            {{ strtoupper(substr($officer->name, 0, 1)) }}
                                        </div>
                                    @endif
                                </div>

                                <!-- Officer Info -->
                                <div class="mb-4">
                                    <h3 class="font-semibold text-custom-darkest mb-1">{{ $officer->name }}</h3>
                                    <p class="text-sm text-custom-dark mb-2">{{ $officer->organization->name }}</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                        {{ ucfirst($officer->pivot->role) }}
                                    </span>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex space-x-2">
                                    @auth
                                        @if(auth()->id() !== $officer->id)
                                            @php
                                                $isFollowing = auth()->user()->following()->where('followed_id', $officer->id)->exists();
                                            @endphp

                                            @if($isFollowing)
                                                <form action="{{ route('users.unfollow', $officer) }}" method="POST" class="flex-1">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                                                        Following
                                                    </button>
                                                </form>
                                            @else
                                                <form action="{{ route('users.follow', $officer) }}" method="POST" class="flex-1">
                                                    @csrf
                                                    <button type="submit" class="w-full bg-custom-green text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-600 transition-colors">
                                                        Follow
                                                    </button>
                                                </form>
                                            @endif
                                        @endif
                                    @endauth

                                    <a href="{{ route('profile.show', $officer) }}"
                                       class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors text-center">
                                        View Profile
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-custom-dark mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Officers</h3>
                        <p class="text-custom-dark">No organization officers are currently registered for this campus.</p>
                    </div>
                @endif
            </div>

            <!-- Posts Tab -->
            <div id="content-posts" class="tab-content" style="display: none;">
                @if($posts->isNotEmpty())
                    <div class="space-y-6">
                        @foreach($posts as $post)
                            <x-post-card :post="$post" />
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-custom-dark mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Posts</h3>
                        <p class="text-custom-dark">No recent posts from students in this campus.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
[x-cloak] {
    display: none !important;
}

.tab-content {
    min-height: 200px;
    padding: 1rem;
}

.tab-content[style*="block"] {
    display: block !important;
}



.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.style.display = 'none';
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.className = button.className.replace('border-custom-green text-custom-green', 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300');
    });

    // Show selected tab content
    const activeContent = document.getElementById('content-' + tabName);
    if (activeContent) {
        activeContent.style.display = 'block';
    }

    // Add active class to selected tab button
    const activeButton = document.getElementById('tab-' + tabName);
    if (activeButton) {
        activeButton.className = activeButton.className.replace('border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300', 'border-custom-green text-custom-green');
    }
}

// Show overview tab by default when page loads
document.addEventListener('DOMContentLoaded', function() {
    showTab('overview');
});
</script>
</x-layouts.unilink-layout>
