<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> <?php echo e($campus->name); ?> - <?php echo e($campus->school->abbreviation ?? 'University'); ?> <?php $__env->endSlot(); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Breadcrumb -->
    <div class="flex items-center space-x-2 text-sm text-custom-dark mb-6">
        <a href="<?php echo e(route('university.index')); ?>" class="hover:text-custom-green">University</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <a href="<?php echo e(route('university.campuses')); ?>" class="hover:text-custom-green">Campuses</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <span><?php echo e($campus->name); ?></span>
    </div>

    <!-- Campus Header -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest mb-6 overflow-hidden">
        <!-- Campus Image -->
        <div class="relative h-64 bg-gray-200">
            <img src="<?php echo e($campus->image_path); ?>" 
                 alt="<?php echo e($campus->name); ?>" 
                 class="w-full h-full object-cover">
            <?php if($campus->is_main_campus): ?>
                <div class="absolute top-4 left-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-custom-green text-white">
                        Main Campus
                    </span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Campus Info -->
        <div class="p-6">
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-custom-darkest mb-2"><?php echo e($campus->name); ?></h1>
                    <?php if($campus->description): ?>
                        <p class="text-custom-dark mb-4"><?php echo e($campus->description); ?></p>
                    <?php endif; ?>
                    <?php if($campus->address): ?>
                        <div class="flex items-start space-x-2">
                            <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <p class="text-custom-dark"><?php echo e($campus->address); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Profile','class' => 'w-6 h-6 text-blue-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Profile','class' => 'w-6 h-6 text-blue-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Students</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['total_students'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Organizations','class' => 'w-6 h-6 text-green-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Organizations','class' => 'w-6 h-6 text-green-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Organizations</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['total_organizations'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Groups','class' => 'w-6 h-6 text-purple-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Groups','class' => 'w-6 h-6 text-purple-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Groups</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['total_groups'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Recent Posts</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['recent_posts'])); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabbed Content -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest" x-data="{ activeTab: 'overview' }">
        <!-- Tab Navigation -->
        <div class="border-b border-custom-second-darkest">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button @click="activeTab = 'overview'" 
                        :class="activeTab === 'overview' ? 'border-custom-green text-custom-green' : 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Overview
                </button>
                <button @click="activeTab = 'organizations'" 
                        :class="activeTab === 'organizations' ? 'border-custom-green text-custom-green' : 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Organizations (<?php echo e($organizations->count()); ?>)
                </button>
                <button @click="activeTab = 'groups'" 
                        :class="activeTab === 'groups' ? 'border-custom-green text-custom-green' : 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Groups (<?php echo e($groups->count()); ?>)
                </button>
                <button @click="activeTab = 'students'" 
                        :class="activeTab === 'students' ? 'border-custom-green text-custom-green' : 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Students (<?php echo e($students->count()); ?>)
                </button>
                <button @click="activeTab = 'posts'" 
                        :class="activeTab === 'posts' ? 'border-custom-green text-custom-green' : 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Posts (<?php echo e($posts->count()); ?>)
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Overview Tab -->
            <div x-show="activeTab === 'overview'" x-transition>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Campus Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-custom-darkest mb-4">Campus Information</h3>
                        <div class="space-y-4">
                            <?php if($campus->description): ?>
                                <div>
                                    <p class="text-sm font-medium text-custom-darkest">Description</p>
                                    <p class="text-custom-dark mt-1"><?php echo e($campus->description); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($campus->address): ?>
                                <div>
                                    <p class="text-sm font-medium text-custom-darkest">Address</p>
                                    <p class="text-custom-dark mt-1"><?php echo e($campus->address); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if($campus->contact_info): ?>
                                <?php if(isset($campus->contact_info['phone'])): ?>
                                    <div>
                                        <p class="text-sm font-medium text-custom-darkest">Phone</p>
                                        <p class="text-custom-dark mt-1"><?php echo e($campus->contact_info['phone']); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if(isset($campus->contact_info['email'])): ?>
                                    <div>
                                        <p class="text-sm font-medium text-custom-darkest">Email</p>
                                        <a href="mailto:<?php echo e($campus->contact_info['email']); ?>" class="text-custom-green hover:underline mt-1 block"><?php echo e($campus->contact_info['email']); ?></a>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div>
                        <h3 class="text-lg font-semibold text-custom-darkest mb-4">Quick Statistics</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Total Students</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['total_students'])); ?></span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Active Organizations</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['total_organizations'])); ?></span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Student Groups</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['total_groups'])); ?></span>
                            </div>

            <!-- Organizations Tab -->
            <div x-show="activeTab === 'organizations'" x-transition>
                <?php if($organizations->isNotEmpty()): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-custom-second-darkest rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center space-x-3 mb-3">
                                    <?php if($organization->logo): ?>
                                        <img src="<?php echo e(asset('storage/' . $organization->logo)); ?>" alt="<?php echo e($organization->name); ?>" class="w-12 h-12 rounded-lg object-cover">
                                    <?php else: ?>
                                        <div class="w-12 h-12 bg-custom-green rounded-lg flex items-center justify-center">
                                            <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Organizations','class' => 'w-6 h-6 text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Organizations','class' => 'w-6 h-6 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-custom-darkest"><?php echo e($organization->name); ?></h4>
                                        <p class="text-sm text-custom-dark"><?php echo e($organization->active_members_count); ?> members</p>
                                    </div>
                                </div>
                                <?php if($organization->description): ?>
                                    <p class="text-sm text-custom-dark mb-3 line-clamp-2"><?php echo e($organization->description); ?></p>
                                <?php endif; ?>
                                <a href="<?php echo e(route('organizations.show', $organization)); ?>" class="text-custom-green hover:underline text-sm">View Organization →</a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Organizations','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Organizations','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Organizations</h3>
                        <p class="text-custom-dark">No organizations are currently registered for this campus.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Groups Tab -->
            <div x-show="activeTab === 'groups'" x-transition>
                <?php if($groups->isNotEmpty()): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-custom-second-darkest rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center space-x-3 mb-3">
                                    <?php if($group->logo): ?>
                                        <img src="<?php echo e(asset('storage/' . $group->logo)); ?>" alt="<?php echo e($group->name); ?>" class="w-12 h-12 rounded-lg object-cover">
                                    <?php else: ?>
                                        <div class="w-12 h-12 bg-custom-green rounded-lg flex items-center justify-center">
                                            <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Groups','class' => 'w-6 h-6 text-white']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Groups','class' => 'w-6 h-6 text-white']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-custom-darkest"><?php echo e($group->name); ?></h4>
                                        <p class="text-sm text-custom-dark"><?php echo e($group->active_members_count); ?> members</p>
                                    </div>
                                </div>
                                <?php if($group->description): ?>
                                    <p class="text-sm text-custom-dark mb-3 line-clamp-2"><?php echo e($group->description); ?></p>
                                <?php endif; ?>
                                <div class="flex items-center justify-between">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                        <?php echo e(ucfirst($group->visibility)); ?>

                                    </span>
                                    <a href="<?php echo e(route('groups.show', $group)); ?>" class="text-custom-green hover:underline text-sm">View Group →</a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Groups','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Groups','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Groups</h3>
                        <p class="text-custom-dark">No student groups are currently active for this campus.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Students Tab -->
            <div x-show="activeTab === 'students'" x-transition>
                <?php if($students->isNotEmpty()): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-custom-second-darkest rounded-lg p-4 text-center hover:shadow-md transition-shadow duration-200">
                                <?php if($student->avatar): ?>
                                    <img src="<?php echo e(asset('storage/' . $student->avatar)); ?>" alt="<?php echo e($student->name); ?>" class="w-16 h-16 rounded-full mx-auto mb-3 object-cover">
                                <?php else: ?>
                                    <div class="w-16 h-16 bg-custom-green rounded-full mx-auto mb-3 flex items-center justify-center">
                                        <span class="text-white font-semibold text-lg"><?php echo e(substr($student->name, 0, 1)); ?></span>
                                    </div>
                                <?php endif; ?>
                                <h4 class="font-semibold text-custom-darkest mb-1"><?php echo e($student->name); ?></h4>
                                <?php if($student->course_program): ?>
                                    <p class="text-sm text-custom-dark mb-2"><?php echo e($student->course_program); ?></p>
                                <?php endif; ?>
                                <?php if($student->year_level): ?>
                                    <p class="text-xs text-custom-dark mb-3"><?php echo e($student->year_level); ?></p>
                                <?php endif; ?>
                                <a href="<?php echo e(route('profile.user', $student)); ?>" class="text-custom-green hover:underline text-sm">View Profile →</a>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Profile','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Profile','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Students</h3>
                        <p class="text-custom-dark">No students are currently registered for this campus.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Posts Tab -->
            <div x-show="activeTab === 'posts'" x-transition>
                <?php if($posts->isNotEmpty()): ?>
                    <div class="space-y-6">
                        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-custom-second-darkest rounded-lg p-4">
                                <div class="flex items-center space-x-3 mb-3">
                                    <?php if($post->user->avatar): ?>
                                        <img src="<?php echo e(asset('storage/' . $post->user->avatar)); ?>" alt="<?php echo e($post->user->name); ?>" class="w-10 h-10 rounded-full object-cover">
                                    <?php else: ?>
                                        <div class="w-10 h-10 bg-custom-green rounded-full flex items-center justify-center">
                                            <span class="text-white font-semibold text-sm"><?php echo e(substr($post->user->name, 0, 1)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-custom-darkest"><?php echo e($post->user->name); ?></h4>
                                        <p class="text-sm text-custom-dark"><?php echo e($post->created_at->diffForHumans()); ?></p>
                                    </div>
                                </div>

                                <?php if($post->title): ?>
                                    <h3 class="font-semibold text-custom-darkest mb-2"><?php echo e($post->title); ?></h3>
                                <?php endif; ?>

                                <p class="text-custom-dark mb-3 line-clamp-3"><?php echo e($post->content); ?></p>

                                <?php if($post->tags->isNotEmpty()): ?>
                                    <div class="flex flex-wrap gap-2 mb-3">
                                        <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-custom-lightest text-custom-darkest">
                                                <?php echo e($tag->name); ?>

                                            </span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>

                                <div class="flex items-center justify-between text-sm text-custom-dark">
                                    <div class="flex items-center space-x-4">
                                        <span><?php echo e($post->reactions_count); ?> reactions</span>
                                        <span><?php echo e($post->comments_count); ?> comments</span>
                                    </div>
                                    <a href="<?php echo e(route('posts.show', $post)); ?>" class="text-custom-green hover:underline">View Post →</a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-custom-dark mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Posts</h3>
                        <p class="text-custom-dark">No recent posts from students in this campus.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/university/campus.blade.php ENDPATH**/ ?>