<?php if (isset($component)) { $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9 = $attributes; } ?>
<?php $component = App\View\Components\Layouts\UnilinkLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layouts.unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layouts\UnilinkLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> <?php echo e($campus->name); ?> - <?php echo e($campus->school->abbreviation ?? 'University'); ?> <?php $__env->endSlot(); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Breadcrumb -->
    <div class="flex items-center space-x-2 text-sm text-custom-dark mb-6">
        <a href="<?php echo e(route('university.index')); ?>" class="hover:text-custom-green">University</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <a href="<?php echo e(route('university.campuses')); ?>" class="hover:text-custom-green">Campuses</a>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
        <span><?php echo e($campus->name); ?></span>
    </div>

    <!-- Campus Header -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest mb-6 overflow-hidden">
        <!-- Campus Image -->
        <div class="relative h-64 bg-gray-200">
            <img src="<?php echo e($campus->image_path); ?>" 
                 alt="<?php echo e($campus->name); ?>" 
                 class="w-full h-full object-cover">
            <?php if($campus->is_main_campus): ?>
                <div class="absolute top-4 left-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-custom-green text-white">
                        Main Campus
                    </span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Campus Info -->
        <div class="p-6">
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-custom-darkest mb-2"><?php echo e($campus->name); ?></h1>
                    <?php if($campus->description): ?>
                        <p class="text-custom-dark mb-4"><?php echo e($campus->description); ?></p>
                    <?php endif; ?>
                    <?php if($campus->address): ?>
                        <div class="flex items-start space-x-2">
                            <svg class="w-5 h-5 text-custom-dark mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <p class="text-custom-dark"><?php echo e($campus->address); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Profile','class' => 'w-6 h-6 text-blue-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Profile','class' => 'w-6 h-6 text-blue-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Students</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['total_students'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Organizations','class' => 'w-6 h-6 text-green-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Organizations','class' => 'w-6 h-6 text-green-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Organizations</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['total_organizations'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100">
                    <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Groups','class' => 'w-6 h-6 text-purple-600']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Groups','class' => 'w-6 h-6 text-purple-600']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Groups</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['total_groups'])); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-orange-100">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-custom-dark">Recent Posts</p>
                    <p class="text-2xl font-bold text-custom-darkest"><?php echo e(number_format($stats['recent_posts'])); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabbed Content -->
    <div class="bg-white rounded-lg shadow-sm border border-custom-second-darkest" id="campus-tabs">
        <!-- Tab Navigation -->
        <div class="border-b border-custom-second-darkest">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button onclick="showTab('overview')" id="tab-overview"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-custom-green text-custom-green">
                    Overview
                </button>
                <button onclick="showTab('organizations')" id="tab-organizations"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Organizations (<?php echo e($organizations->count()); ?>)
                </button>
                <button onclick="showTab('groups')" id="tab-groups"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Groups (<?php echo e($groups->count()); ?>)
                </button>
                <button onclick="showTab('students')" id="tab-students"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Students (<?php echo e($students->count()); ?>)
                </button>
                <button onclick="showTab('posts')" id="tab-posts"
                        class="tab-button whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300">
                    Posts (<?php echo e($posts->count()); ?>)
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Temporary Debug Info -->
            <div class="mb-4 p-3 bg-yellow-100 border border-yellow-400 rounded text-sm">
                <strong>Debug Info:</strong><br>
                Students Collection Count: <?php echo e($students->count()); ?><br>
                Organizations Collection Count: <?php echo e($organizations->count()); ?><br>
                Groups Collection Count: <?php echo e($groups->count()); ?><br>
                Posts Collection Count: <?php echo e($posts->count()); ?><br>
                <br>
                <strong>Students in this campus:</strong><br>
                <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    - <?php echo e($student->name); ?> (ID: <?php echo e($student->id); ?>, Role: <?php echo e($student->role); ?>)<br>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Overview Tab -->
            <div id="content-overview" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Campus Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-custom-darkest mb-4">Campus Information</h3>
                        <div class="space-y-4">
                            <?php if($campus->description): ?>
                                <div>
                                    <p class="text-sm font-medium text-custom-darkest">Description</p>
                                    <p class="text-custom-dark mt-1"><?php echo e($campus->description); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <?php if($campus->address): ?>
                                <div>
                                    <p class="text-sm font-medium text-custom-darkest">Address</p>
                                    <p class="text-custom-dark mt-1"><?php echo e($campus->address); ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if($campus->contact_info): ?>
                                <?php if(isset($campus->contact_info['phone'])): ?>
                                    <div>
                                        <p class="text-sm font-medium text-custom-darkest">Phone</p>
                                        <p class="text-custom-dark mt-1"><?php echo e($campus->contact_info['phone']); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if(isset($campus->contact_info['email'])): ?>
                                    <div>
                                        <p class="text-sm font-medium text-custom-darkest">Email</p>
                                        <a href="mailto:<?php echo e($campus->contact_info['email']); ?>" class="text-custom-green hover:underline mt-1 block"><?php echo e($campus->contact_info['email']); ?></a>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div>
                        <h3 class="text-lg font-semibold text-custom-darkest mb-4">Quick Statistics</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Total Students</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['total_students'])); ?></span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Active Organizations</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['total_organizations'])); ?></span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Student Groups</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['total_groups'])); ?></span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-custom-lightest rounded-lg">
                                <span class="text-custom-darkest">Recent Posts</span>
                                <span class="font-semibold text-custom-darkest"><?php echo e(number_format($stats['recent_posts'])); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Organizations Tab -->
            <div id="content-organizations" class="tab-content" style="display: none;">
                <?php if($organizations->isNotEmpty()): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                                <!-- Cover Image -->
                                <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                                    <?php if($organization->cover_image): ?>
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image)); ?>" alt="<?php echo e($organization->name); ?>" class="w-full h-full object-cover">
                                    <?php endif; ?>

                                    <!-- Logo -->
                                    <div class="absolute -bottom-6 left-4">
                                        <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                                            <?php if($organization->logo): ?>
                                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo)); ?>" alt="<?php echo e($organization->name); ?>" class="w-10 h-10 rounded-lg object-cover">
                                            <?php else: ?>
                                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <span class="text-blue-600 font-semibold text-sm"><?php echo e(substr($organization->name, 0, 2)); ?></span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Content -->
                                <div class="p-4 pt-8">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900 truncate"><?php echo e($organization->name); ?></h3>
                                        <span class="text-sm text-gray-500"><?php echo e($organization->active_members_count); ?> members</span>
                                    </div>

                                    <?php if($organization->description): ?>
                                        <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e(Str::limit($organization->description, 100)); ?></p>
                                    <?php endif; ?>

                                    <?php if($organization->creator): ?>
                                        <p class="text-xs text-gray-500 mb-4">Created by <?php echo e($organization->creator->name); ?></p>
                                    <?php endif; ?>

                                    <!-- Action Buttons -->
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('organizations.show', $organization)); ?>" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 text-sm font-medium">
                                            View Details
                                        </a>
                                        <?php if(auth()->guard()->check()): ?>
                                            <?php
                                                $userMembership = $organization->members()->where('user_id', auth()->id())->first();
                                                $isFollowing = auth()->user()->followedOrganizations()->where('organization_id', $organization->id)->exists();
                                            ?>

                                            <?php if($userMembership && $userMembership->pivot->status === 'active'): ?>
                                                <button disabled class="flex-1 bg-gray-100 text-gray-600 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                                    Member
                                                </button>
                                            <?php elseif($userMembership && $userMembership->pivot->status === 'pending'): ?>
                                                <button disabled class="flex-1 bg-yellow-100 text-yellow-600 py-2 px-4 rounded-lg text-sm font-medium cursor-not-allowed">
                                                    Pending
                                                </button>
                                            <?php else: ?>
                                                <form action="<?php echo e(route('organizations.join', $organization)); ?>" method="POST" class="flex-1">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 text-sm font-medium">
                                                        Join
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Organizations','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Organizations','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Organizations</h3>
                        <p class="text-custom-dark">No organizations are currently registered for this campus.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Groups Tab -->
            <div id="content-groups" class="tab-content" style="display: none;">
                <?php if($groups->isNotEmpty()): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                                <!-- Cover Image -->
                                <div class="h-32 bg-gradient-to-r from-green-500 to-blue-600 relative">
                                    <?php if($group->cover_image): ?>
                                        <img src="<?php echo e(Storage::disk('public')->url($group->cover_image)); ?>"
                                             alt="<?php echo e($group->name); ?>"
                                             class="w-full h-full object-cover">
                                    <?php endif; ?>

                                    <!-- Privacy Badge -->
                                    <div class="absolute top-3 right-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?php echo e($group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                            <?php echo e(ucfirst($group->visibility)); ?>

                                        </span>
                                    </div>
                                </div>

                                <div class="p-4">
                                    <!-- Group Logo and Info -->
                                    <div class="flex items-start space-x-3 -mt-4 mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center border-2 border-white">
                                                <?php if($group->logo): ?>
                                                    <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>"
                                                         alt="<?php echo e($group->name); ?>"
                                                         class="w-10 h-10 rounded-lg object-cover">
                                                <?php else: ?>
                                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                        <span class="text-blue-600 font-bold text-sm"><?php echo e(substr($group->name, 0, 2)); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="flex-1 min-w-0 pt-2">
                                            <h3 class="text-lg font-semibold text-gray-900 truncate">
                                                <a href="<?php echo e(route('groups.show', $group)); ?>" class="hover:text-blue-600">
                                                    <?php echo e($group->name); ?>

                                                </a>
                                            </h3>
                                            <?php if($group->organization): ?>
                                                <p class="text-sm text-gray-500 truncate"><?php echo e($group->organization->name); ?></p>
                                            <?php endif; ?>
                                            <?php if($group->school || $group->campus): ?>
                                                <p class="text-xs text-gray-400 truncate">
                                                    <?php if($group->school): ?>
                                                        <?php echo e($group->school->abbreviation ?? $group->school->name); ?>

                                                        <?php if($group->campus): ?>
                                                            - <?php echo e($group->campus->name); ?>

                                                        <?php endif; ?>
                                                    <?php elseif($group->campus): ?>
                                                        <?php echo e($group->campus->name); ?>

                                                    <?php endif; ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <!-- Description -->
                                    <?php if($group->description): ?>
                                        <p class="text-gray-600 text-sm mb-3 line-clamp-2"><?php echo e($group->description); ?></p>
                                    <?php endif; ?>

                                    <!-- Stats -->
                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                            </svg>
                                            <?php echo e($group->active_members_count); ?> members
                                        </div>
                                        <?php if($group->creator): ?>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                                <?php echo e($group->creator->name); ?>

                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Action Button -->
                                    <div class="flex justify-end">
                                        <a href="<?php echo e(route('groups.show', $group)); ?>"
                                           class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                                            View Group
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Groups','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Groups','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Groups</h3>
                        <p class="text-custom-dark">No student groups are currently active for this campus.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Students Tab -->
            <div id="content-students" class="tab-content" style="display: none;">
                <?php if($students->isNotEmpty()): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <?php $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center hover:shadow-md transition-shadow duration-200">
                                <!-- Profile Picture -->
                                <div class="relative mb-4">
                                    <?php if($student->avatar): ?>
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($student->avatar)); ?>"
                                             alt="<?php echo e($student->name); ?>"
                                             class="w-20 h-20 rounded-full mx-auto object-cover border-4 border-white shadow-md">
                                    <?php else: ?>
                                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto flex items-center justify-center border-4 border-white shadow-md">
                                            <span class="text-white font-bold text-2xl"><?php echo e(substr($student->name, 0, 1)); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Student Info -->
                                <div class="space-y-2">
                                    <h4 class="text-lg font-semibold text-gray-900"><?php echo e($student->name); ?></h4>

                                    <?php if($student->course_program): ?>
                                        <p class="text-sm text-gray-600 font-medium"><?php echo e($student->course_program); ?></p>
                                    <?php endif; ?>

                                    <?php if($student->year_level): ?>
                                        <p class="text-xs text-gray-500"><?php echo e($student->year_level); ?></p>
                                    <?php endif; ?>

                                    <?php if($student->college_department): ?>
                                        <p class="text-xs text-gray-500"><?php echo e($student->college_department); ?></p>
                                    <?php endif; ?>

                                    <!-- Campus Info -->
                                    <div class="flex items-center justify-center text-xs text-gray-400 mt-2">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                        <?php echo e($campus->name); ?>

                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="mt-4 flex space-x-2">
                                    <a href="<?php echo e(route('profile.user', $student)); ?>"
                                       class="flex-1 bg-blue-600 text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">
                                        View Profile
                                    </a>
                                    <?php if(auth()->guard()->check()): ?>
                                        <?php if(auth()->id() !== $student->id): ?>
                                            <?php if(auth()->user()->isFollowingUser($student)): ?>
                                                <form action="<?php echo e(route('users.unfollow', $student)); ?>" method="POST" class="flex-1">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('DELETE'); ?>
                                                    <button type="submit" class="w-full bg-gray-200 text-gray-700 py-2 px-3 rounded-md text-sm font-medium hover:bg-gray-300 transition-colors">
                                                        Following
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form action="<?php echo e(route('users.follow', $student)); ?>" method="POST" class="flex-1">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="w-full bg-green-600 text-white py-2 px-3 rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
                                                        Follow
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <?php if (isset($component)) { $__componentOriginal4fc2bce81e6643975498c1caac0c8935 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4fc2bce81e6643975498c1caac0c8935 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.svg-icon','data' => ['name' => 'Profile','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('svg-icon'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'Profile','class' => 'w-12 h-12 text-custom-dark mx-auto mb-4']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $attributes = $__attributesOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__attributesOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4fc2bce81e6643975498c1caac0c8935)): ?>
<?php $component = $__componentOriginal4fc2bce81e6643975498c1caac0c8935; ?>
<?php unset($__componentOriginal4fc2bce81e6643975498c1caac0c8935); ?>
<?php endif; ?>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Students</h3>
                        <p class="text-custom-dark">No students are currently registered for this campus.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Posts Tab -->
            <div id="content-posts" class="tab-content" style="display: none;">
                <?php if($posts->isNotEmpty()): ?>
                    <div class="space-y-6">
                        <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (isset($component)) { $__componentOriginal14b498b52c33a1421ff8895e4557790f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal14b498b52c33a1421ff8895e4557790f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.post-card','data' => ['post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('post-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $attributes = $__attributesOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__attributesOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal14b498b52c33a1421ff8895e4557790f)): ?>
<?php $component = $__componentOriginal14b498b52c33a1421ff8895e4557790f; ?>
<?php unset($__componentOriginal14b498b52c33a1421ff8895e4557790f); ?>
<?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-custom-dark mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                        <h3 class="text-lg font-medium text-custom-darkest mb-2">No Posts</h3>
                        <p class="text-custom-dark">No recent posts from students in this campus.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
[x-cloak] {
    display: none !important;
}

.tab-content {
    min-height: 200px;
    padding: 1rem;
}

.tab-content[style*="block"] {
    display: block !important;
}



.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>

<script>
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.style.display = 'none';
    });

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.className = button.className.replace('border-custom-green text-custom-green', 'border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300');
    });

    // Show selected tab content
    const activeContent = document.getElementById('content-' + tabName);
    if (activeContent) {
        activeContent.style.display = 'block';
    }

    // Add active class to selected tab button
    const activeButton = document.getElementById('tab-' + tabName);
    if (activeButton) {
        activeButton.className = activeButton.className.replace('border-transparent text-custom-dark hover:text-custom-darkest hover:border-gray-300', 'border-custom-green text-custom-green');
    }
}

// Show overview tab by default when page loads
document.addEventListener('DOMContentLoaded', function() {
    showTab('overview');
});
</script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $attributes = $__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__attributesOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9)): ?>
<?php $component = $__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9; ?>
<?php unset($__componentOriginal283b9f021c038c6d2eeddbfd373ad7a9); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/university/campus.blade.php ENDPATH**/ ?>